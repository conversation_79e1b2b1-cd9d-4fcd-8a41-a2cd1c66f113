"use client";

import { MenuItem } from "@/types/navigation";
import { cn } from "@/lib/utils";
import { useAuthStore } from "@/store/auth-store";
import { AlignJustify, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import BagModal from "../bag/bag-modal";
import NotificationBellIcon from "../icons/notification-bell-icon";
import { Button } from "../ui/button";
import { DesktopMenuItem } from "./desktop-menu-item";
import { MobileMenuItem } from "./mobile-menu-item";
import NavBagIcon from "./nav-bag-icon";
import RevolvedLogo from "./nav-bar-logo";
import { useGetPublicCategories } from "@/api/category-service";

const NavBar = () => {
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { isAuthenticated } = useAuthStore();
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);

  const { categories } = useGetPublicCategories({ limit: 100, page: 0 });

  const toggleDropdown = useCallback(
    (index: number) => {
      setActiveDropdown(activeDropdown === index ? null : index);
    },
    [activeDropdown]
  );

  const MENU_ITEMS: MenuItem[] = [
    // { label: "Home", hasDropdown: false, href: "/" },
    {
      label: "Treatments",
      hasDropdown: true,
      href: "/treatments",
      subItems: [
        ...categories.map((category) => ({
          label: category.name,
          href: `/treatments/${category.slug}`,
          subItems:
            category.slug === "skincare"
              ? [
                  {
                    label: "Acne",
                    href: `/treatments/${category.slug}?subcategory=acne`,
                  },
                  {
                    label: "Wrinkles & Aging",
                    href: `/treatments/${category.slug}?subcategory=wrinkles-aging`,
                  },
                  {
                    label: "Pigmentation",
                    href: `/treatments/${category.slug}?subcategory=pingmentation`,
                  },
                  {
                    label: "Excessive Sweating",
                    href: `/treatments/${category.slug}?subcategory=excessive-sweating`,
                  },
                ]
              : [],
        })),
      ],
    },
    { label: "About", hasDropdown: false, href: "/about" },
    { label: "How it works", hasDropdown: false, href: "/#how-it-works" },
    { label: "Everyday Essentials", hasDropdown: false, href: "/shop" },
  ];

  const PROTECTED_MENU_ITEMS: MenuItem[] = [
    {
      label: "My treatments",
      hasDropdown: false,
      href: "/profile?tab=my-treatments",
      subItems: [],
    },
    // { label: "My orders", hasDropdown: false, href: "/my-orders", subItems: [] },
  ];

  return (
    <nav className="font-quinn relative w-full">
      {/* Background */}
      <div className="fixed top-0 left-0 z-[100] h-16 w-full md:h-20">
        <div className="absolute inset-0 bg-white/80 backdrop-blur-[50px]" />
        <div className="relative mx-auto h-full max-w-[2560px] px-4 lg:px-16">
          <div className="flex h-full items-center justify-between">
            {/* Logo */}
            <Link href="/" className="relative h-8 w-32 overflow-hidden">
              <RevolvedLogo />
            </Link>

            {/* Desktop Menu */}
            <div className="hidden items-center gap-5 lg:flex">
              {MENU_ITEMS.map((item, index) => (
                <div key={index} className="relative">
                  <DesktopMenuItem
                    item={item}
                    index={index}
                    activeDropdown={activeDropdown}
                    onDropdownToggle={toggleDropdown}
                  />
                </div>
              ))}
            </div>

            {/* Right Side Actions */}
            <div className={`flex items-center ${isAuthenticated ? "gap-4" : "gap-2"}`}>
              <Button
                variant="primary"
                onClick={() => router.push("/questionnaire")}
                withArrow
                className="hidden md:flex"
              >
                Consult Now
              </Button>

              {/* Sign In Button */}

              <div className="hidden items-center gap-4 lg:flex">
                {isAuthenticated &&
                  PROTECTED_MENU_ITEMS.map((item, index) => (
                    <div key={index} className="relative">
                      <DesktopMenuItem
                        item={item}
                        index={index}
                        activeDropdown={activeDropdown}
                        onDropdownToggle={toggleDropdown}
                      />
                    </div>
                  ))}
              </div>
              {!isAuthenticated && (
                <Button variant="primary" onClick={() => router.push("/signin")} withArrow>
                  Sign In
                </Button>
              )}

              <BagModal trigger={<NavBagIcon />} />

              {isAuthenticated && (
                <button className="mt-1 flex size-8 items-center justify-center rounded-full transition-colors duration-200 hover:bg-gray-50">
                  <NotificationBellIcon className="size-6 text-black" />
                </button>
              )}

              {isAuthenticated && (
                <Link
                  href="/profile"
                  className="flex size-5 items-center justify-center rounded-full transition-colors duration-200 hover:bg-gray-50"
                >
                  <Image
                    src="/icons/user-icon.svg"
                    alt="User account"
                    width={16}
                    height={16}
                    className="size-4.5 text-black"
                  />
                </Link>
              )}
              <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="lg:hidden">
                {isMenuOpen ? <X /> : <AlignJustify />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={cn(
          "fixed top-16 left-0 z-40 w-full bg-white/80 backdrop-blur-[50px] transition-all duration-300 ease-in-out md:top-24 lg:hidden",
          isMenuOpen
            ? "translate-y-0 transform opacity-100"
            : "pointer-events-none -translate-y-4 transform opacity-0"
        )}
      >
        <div className="mx-auto max-w-[1728px] px-4 py-4">
          {MENU_ITEMS.map((item, index) => (
            <MobileMenuItem
              key={index}
              item={item}
              index={index}
              activeDropdown={activeDropdown}
              onDropdownToggle={toggleDropdown}
              closeMenu={() => setIsMenuOpen(false)}
            />
          ))}
          {isAuthenticated &&
            PROTECTED_MENU_ITEMS.map((item, index) => (
              <MobileMenuItem
                key={index}
                item={item}
                index={index}
                activeDropdown={activeDropdown}
                onDropdownToggle={toggleDropdown}
                closeMenu={() => setIsMenuOpen(false)}
              />
            ))}
          <div className="mt-4">
            <Button
              variant="primary"
              className="w-full md:hidden"
              withArrow
              onClick={() => router.push("/questionnaire")}
            >
              Consult Now
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default NavBar;
